{"sourceFile": "services/scripts/public/recibirAutorizacionMercadoLibre.php", "activeCommit": 0, "commits": [{"activePatchIndex": 0, "patches": [{"date": 1757451395979, "content": "Index: \n===================================================================\n--- \n+++ \n"}], "date": 1757451395979, "name": "Commit-0", "content": "<?php\nrequire __DIR__.'/../../acc/acc.php';\nrecuperar_sesion();\n\nrequire PATH_TOOLS.'mercadolibre/meli.php';\nrequire PATH_SAAS.'public/librerias/funciones_modelo.php';\n\n$bd_link = conectar_db();\n\n$idtienda = (isset($_SESSION['idtienda']) && is_numeric($_SESSION['idtienda']))\n    ? $_SESSION['idtienda']\n    : 1;\n$tienda = array_sql(consulta_sql(\"SELECT ML_access_token, ML_expires_in, ML_refresh_token FROM tienda WHERE idtienda = '$idtienda'\"));\n$code = recibir_variable('code', true);\n$meli = new Meli(ML_APP_ID, ML_APP_SECRET, $_SESSION['access_token'], $_SESSION['refresh_token']);\n\nfile_put_contents(PATH_LOGS.'api-v0.1/tokens.log',\n    date(\"Y-m-d H:i:s\") . SEPARADOR_CSV\n    . $tienda['ML_user_id'] . SEPARADOR_CSV\n    . 'Respuesta de new Meli: ' . json_encode($meli). SEPARADOR_CSV\n    . json_encode($tienda) . \"\\r\\n\"\n    . json_encode($_SESSION) . \"\\r\\n\"\n    , FILE_APPEND);\n\n// Si estoy entrando a la solicitud, pero ya estoy autorizado, lo cargo en memoria para que se revise que esté actualizada la autorización\nif (!$code && !$_SESSION['access_token'] && $tienda['ML_access_token']) {\n    $_SESSION['access_token'] = $tienda['ML_access_token'];\n    $_SESSION['expires_in'] = $tienda['ML_expires_in'];\n    $_SESSION['refresh_token'] = $tienda['ML_refresh_token'];\n}\n\nif ($code || $_SESSION['access_token']) {\n\n    // If code exist and session is empty\n    if ($code && !$_SESSION['access_token']) {\n        // If the code was in get parameter we authorize\n        $user = $meli->authorize($code, URL_SCRIPTS.'/recibirAutorizacionMercadoLibre.php');\n        $ML_user_id = $user['body']->user_id;\n\n        file_put_contents(PATH_LOGS.'api-v0.1/tokens.log',\n            date(\"Y-m-d H:i:s\") . SEPARADOR_CSV\n            . $tienda['ML_user_id'] . SEPARADOR_CSV\n            . 'Respuesta de authorize: ' . json_encode($user) . ' | '\n            . 'Consulta SQL: SELECT idtienda FROM tienda WHERE ML_user_id = \"'.$ML_user_id.'\"'\n            . 'Respuesta SQL: ' . contar_sql(consulta_sql(\"SELECT idtienda FROM tienda WHERE ML_user_id = '\".$ML_user_id.\"'\")) . SEPARADOR_CSV\n            . json_encode($tienda) . \"\\r\\n\"\n            , FILE_APPEND);\n\n        /* Chequeo si ya existe */\n        if (contar_sql(consulta_sql(\"SELECT idtienda FROM tienda WHERE ML_user_id = '\".$ML_user_id.\"' AND ML_estado = 1 AND ML_access_token != ''\"))) {\n\n            //Ya existe\n            file_put_contents(PATH_LOGS.'api-v0.1/tokens.log',\n            date(\"Y-m-d H:i:s\") . SEPARADOR_CSV\n            . $tienda['ML_user_id'] . SEPARADOR_CSV\n            . 'Ya existe Cuenta de ML Autorizada' . SEPARADOR_CSV\n            . json_encode($_SESSION) . SEPARADOR_CSV\n            . json_encode(\"ML_user_id ya ingresado\") . SEPARADOR_CSV\n            . json_encode($tienda) . \"\\r\\n\"\n            , FILE_APPEND);\n            header('Location: '.$_SESSION['servidor_url'].'configuraciones.php?a=mlno&');\n            exit();\n        }\n\n        //Puedo seguir\n        //$params = array('access_token' => $_SESSION['access_token']);\n        //$resultado_ml = $meli->get('/users/me', $params);\n        //$resultado_json = json_decode($resultado_ml);\n\n\n        // Now we create the sessions with the authenticated user\n        $_SESSION['access_token'] = $user['body']->access_token;\n        $_SESSION['expires_in'] = time() + $user['body']->expires_in;\n        $_SESSION['refresh_token'] = $user['body']->refresh_token;\n\n    } else {\n        // We can check if the access token in invalid checking the time\n        if ($_SESSION['expires_in'] < time()) {\n            try {\n                // Make the refresh proccess\n                $refresh = $meli->refreshAccessToken();\n\n                // Now we create the sessions with the new parameters\n                $_SESSION['access_token'] = $refresh['body']->access_token;\n                $_SESSION['expires_in'] = time() + $refresh['body']->expires_in;\n                $_SESSION['refresh_token'] = $refresh['body']->refresh_token;\n\n            } catch (Exception $e) {\n                mostrar_error(\"Exception: \". $e->getMessage());\n            }\n        }\n    }\n\n    $params = array('access_token' => $_SESSION['access_token']);\n    $resultado_ml = $meli->get('/users/me', $params);\n    $resultado_json = json_decode($resultado_ml);\n\n    consulta_sql(\"UPDATE tienda SET\n            ML_user_id = '{$resultado_ml['body']->{'id'}}',\n            ML_access_token = '{$_SESSION['access_token']}',\n            ML_expires_in = '{$_SESSION['expires_in']}',\n            ML_refresh_token = '{$_SESSION['refresh_token']}'\n        WHERE idtienda = '$idtienda'\");\n\n    // BUSQUEDA de Tokens no renovados\n    file_put_contents(PATH_LOGS.'api-v0.1/tokens.log',\n        date(\"Y-m-d H:i:s\") . SEPARADOR_CSV\n        . $tienda['ML_user_id'] . SEPARADOR_CSV\n        . 'OK refresh_token ML' . SEPARADOR_CSV\n        . json_encode($_SESSION) . SEPARADOR_CSV\n        . json_encode($resultado_json) . SEPARADOR_CSV\n        . json_encode($tienda) . \"\\r\\n\"\n        , FILE_APPEND);\n    // FIN BUSQUEDA\n\n    // Tengo que consultar primero si existe porque no puedo usar otro método ya que consulta_sql cierra la conexión con saasargentina\n    if (!contar_sql(consulta_sql(\n        \"SELECT idempresa FROM tiendas\n        WHERE idempresa = '{$_SESSION['empresa_idempresa']}'\n            AND ML_user_id = '{$resultado_ml['body']->{'id'}}'\n        LIMIT 1\", 'saasargentina')))\n        consulta_sql(\"INSERT INTO tiendas SET\n            ML_user_id = '{$resultado_ml['body']->{'id'}}',\n            idempresa = '{$_SESSION['empresa_idempresa']}'\",\n            'saasargentina');\n\n    unset($_SESSION['idtienda']);\n    unset($_SESSION['access_token']);\n    unset($_SESSION['expires_in']);\n    unset($_SESSION['refresh_token']);\n\n    header('Location: '.$_SESSION['servidor_url'].'configuraciones.php?a=ml');\n\n} else {\n\n    // Determinar el país según la empresa\n    if ($_SESSION['empresa_idempresa'] == 12905) {\n        // Perú\n        $pais_codigo = 'MPE';\n    } else {\n        // Argentina (por defecto)\n        $pais_codigo = 'MLA';\n    }\n\n    // Obtener la URL de autorización desde la clase Meli\n    $auth_url = Meli::$AUTH_URL[$pais_codigo];\n    $redirect_uri = URL_SCRIPTS.'/recibirAutorizacionMercadoLibre.php';\n?>\n        <script>\n            var app_id = '<?php echo ML_APP_ID; ?>';\n            var auth_url = '<?php echo $auth_url; ?>';\n            var redirect_uri = '<?php echo $redirect_uri; ?>';\n            var url = auth_url + '/authorization?response_type=code&client_id=' + app_id + '&redirect_uri=' + encodeURIComponent(redirect_uri);\n            window.location.href = url;\n        </script>\n    <?php\n}\n"}]}