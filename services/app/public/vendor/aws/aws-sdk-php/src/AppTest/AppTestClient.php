<?php
namespace Aws\AppTest;

use Aws\AwsClient;

/**
 * This client is used to interact with the **AWS Mainframe Modernization Application Testing** service.
 * @method \Aws\Result createTestCase(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createTestCaseAsync(array $args = [])
 * @method \Aws\Result createTestConfiguration(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createTestConfigurationAsync(array $args = [])
 * @method \Aws\Result createTestSuite(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createTestSuiteAsync(array $args = [])
 * @method \Aws\Result deleteTestCase(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteTestCaseAsync(array $args = [])
 * @method \Aws\Result deleteTestConfiguration(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteTestConfigurationAsync(array $args = [])
 * @method \Aws\Result deleteTestRun(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteTestRunAsync(array $args = [])
 * @method \Aws\Result deleteTestSuite(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteTestSuiteAsync(array $args = [])
 * @method \Aws\Result getTestCase(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getTestCaseAsync(array $args = [])
 * @method \Aws\Result getTestConfiguration(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getTestConfigurationAsync(array $args = [])
 * @method \Aws\Result getTestRunStep(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getTestRunStepAsync(array $args = [])
 * @method \Aws\Result getTestSuite(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getTestSuiteAsync(array $args = [])
 * @method \Aws\Result listTagsForResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listTagsForResourceAsync(array $args = [])
 * @method \Aws\Result listTestCases(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listTestCasesAsync(array $args = [])
 * @method \Aws\Result listTestConfigurations(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listTestConfigurationsAsync(array $args = [])
 * @method \Aws\Result listTestRunSteps(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listTestRunStepsAsync(array $args = [])
 * @method \Aws\Result listTestRunTestCases(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listTestRunTestCasesAsync(array $args = [])
 * @method \Aws\Result listTestRuns(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listTestRunsAsync(array $args = [])
 * @method \Aws\Result listTestSuites(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listTestSuitesAsync(array $args = [])
 * @method \Aws\Result startTestRun(array $args = [])
 * @method \GuzzleHttp\Promise\Promise startTestRunAsync(array $args = [])
 * @method \Aws\Result tagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise tagResourceAsync(array $args = [])
 * @method \Aws\Result untagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise untagResourceAsync(array $args = [])
 * @method \Aws\Result updateTestCase(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateTestCaseAsync(array $args = [])
 * @method \Aws\Result updateTestConfiguration(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateTestConfigurationAsync(array $args = [])
 * @method \Aws\Result updateTestSuite(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateTestSuiteAsync(array $args = [])
 */
class AppTestClient extends AwsClient {}
