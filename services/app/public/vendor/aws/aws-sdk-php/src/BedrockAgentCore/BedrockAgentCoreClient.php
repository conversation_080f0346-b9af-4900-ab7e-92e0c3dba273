<?php
namespace Aws\BedrockAgentCore;

use Aws\AwsClient;

/**
 * This client is used to interact with the **Amazon Bedrock AgentCore Data Plane Fronting Layer** service.
 * @method \Aws\Result createEvent(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createEventAsync(array $args = [])
 * @method \Aws\Result deleteEvent(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteEventAsync(array $args = [])
 * @method \Aws\Result deleteMemoryRecord(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteMemoryRecordAsync(array $args = [])
 * @method \Aws\Result getBrowserSession(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getBrowserSessionAsync(array $args = [])
 * @method \Aws\Result getCodeInterpreterSession(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getCodeInterpreterSessionAsync(array $args = [])
 * @method \Aws\Result getEvent(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getEventAsync(array $args = [])
 * @method \Aws\Result getMemoryRecord(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getMemoryRecordAsync(array $args = [])
 * @method \Aws\Result getResourceApiKey(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getResourceApiKeyAsync(array $args = [])
 * @method \Aws\Result getResourceOauth2Token(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getResourceOauth2TokenAsync(array $args = [])
 * @method \Aws\Result getWorkloadAccessToken(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getWorkloadAccessTokenAsync(array $args = [])
 * @method \Aws\Result getWorkloadAccessTokenForJWT(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getWorkloadAccessTokenForJWTAsync(array $args = [])
 * @method \Aws\Result getWorkloadAccessTokenForUserId(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getWorkloadAccessTokenForUserIdAsync(array $args = [])
 * @method \Aws\Result invokeAgentRuntime(array $args = [])
 * @method \GuzzleHttp\Promise\Promise invokeAgentRuntimeAsync(array $args = [])
 * @method \Aws\Result invokeCodeInterpreter(array $args = [])
 * @method \GuzzleHttp\Promise\Promise invokeCodeInterpreterAsync(array $args = [])
 * @method \Aws\Result listActors(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listActorsAsync(array $args = [])
 * @method \Aws\Result listBrowserSessions(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listBrowserSessionsAsync(array $args = [])
 * @method \Aws\Result listCodeInterpreterSessions(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listCodeInterpreterSessionsAsync(array $args = [])
 * @method \Aws\Result listEvents(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listEventsAsync(array $args = [])
 * @method \Aws\Result listMemoryRecords(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listMemoryRecordsAsync(array $args = [])
 * @method \Aws\Result listSessions(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listSessionsAsync(array $args = [])
 * @method \Aws\Result retrieveMemoryRecords(array $args = [])
 * @method \GuzzleHttp\Promise\Promise retrieveMemoryRecordsAsync(array $args = [])
 * @method \Aws\Result startBrowserSession(array $args = [])
 * @method \GuzzleHttp\Promise\Promise startBrowserSessionAsync(array $args = [])
 * @method \Aws\Result startCodeInterpreterSession(array $args = [])
 * @method \GuzzleHttp\Promise\Promise startCodeInterpreterSessionAsync(array $args = [])
 * @method \Aws\Result stopBrowserSession(array $args = [])
 * @method \GuzzleHttp\Promise\Promise stopBrowserSessionAsync(array $args = [])
 * @method \Aws\Result stopCodeInterpreterSession(array $args = [])
 * @method \GuzzleHttp\Promise\Promise stopCodeInterpreterSessionAsync(array $args = [])
 * @method \Aws\Result updateBrowserStream(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateBrowserStreamAsync(array $args = [])
 */
class BedrockAgentCoreClient extends AwsClient {}
