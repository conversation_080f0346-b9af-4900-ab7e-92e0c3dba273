<?php
namespace Aws\FSx;

use Aws\AwsClient;

/**
 * This client is used to interact with the **Amazon FSx** service.
 * @method \Aws\Result associateFileSystemAliases(array $args = [])
 * @method \GuzzleHttp\Promise\Promise associateFileSystemAliasesAsync(array $args = [])
 * @method \Aws\Result cancelDataRepositoryTask(array $args = [])
 * @method \GuzzleHttp\Promise\Promise cancelDataRepositoryTaskAsync(array $args = [])
 * @method \Aws\Result copyBackup(array $args = [])
 * @method \GuzzleHttp\Promise\Promise copyBackupAsync(array $args = [])
 * @method \Aws\Result copySnapshotAndUpdateVolume(array $args = [])
 * @method \GuzzleHttp\Promise\Promise copySnapshotAndUpdateVolumeAsync(array $args = [])
 * @method \Aws\Result createAndAttachS3AccessPoint(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createAndAttachS3AccessPointAsync(array $args = [])
 * @method \Aws\Result createBackup(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createBackupAsync(array $args = [])
 * @method \Aws\Result createDataRepositoryAssociation(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createDataRepositoryAssociationAsync(array $args = [])
 * @method \Aws\Result createDataRepositoryTask(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createDataRepositoryTaskAsync(array $args = [])
 * @method \Aws\Result createFileCache(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createFileCacheAsync(array $args = [])
 * @method \Aws\Result createFileSystem(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createFileSystemAsync(array $args = [])
 * @method \Aws\Result createFileSystemFromBackup(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createFileSystemFromBackupAsync(array $args = [])
 * @method \Aws\Result createSnapshot(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createSnapshotAsync(array $args = [])
 * @method \Aws\Result createStorageVirtualMachine(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createStorageVirtualMachineAsync(array $args = [])
 * @method \Aws\Result createVolume(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createVolumeAsync(array $args = [])
 * @method \Aws\Result createVolumeFromBackup(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createVolumeFromBackupAsync(array $args = [])
 * @method \Aws\Result deleteBackup(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteBackupAsync(array $args = [])
 * @method \Aws\Result deleteDataRepositoryAssociation(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteDataRepositoryAssociationAsync(array $args = [])
 * @method \Aws\Result deleteFileCache(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteFileCacheAsync(array $args = [])
 * @method \Aws\Result deleteFileSystem(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteFileSystemAsync(array $args = [])
 * @method \Aws\Result deleteSnapshot(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteSnapshotAsync(array $args = [])
 * @method \Aws\Result deleteStorageVirtualMachine(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteStorageVirtualMachineAsync(array $args = [])
 * @method \Aws\Result deleteVolume(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteVolumeAsync(array $args = [])
 * @method \Aws\Result describeBackups(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeBackupsAsync(array $args = [])
 * @method \Aws\Result describeDataRepositoryAssociations(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeDataRepositoryAssociationsAsync(array $args = [])
 * @method \Aws\Result describeDataRepositoryTasks(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeDataRepositoryTasksAsync(array $args = [])
 * @method \Aws\Result describeFileCaches(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeFileCachesAsync(array $args = [])
 * @method \Aws\Result describeFileSystemAliases(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeFileSystemAliasesAsync(array $args = [])
 * @method \Aws\Result describeFileSystems(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeFileSystemsAsync(array $args = [])
 * @method \Aws\Result describeS3AccessPointAttachments(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeS3AccessPointAttachmentsAsync(array $args = [])
 * @method \Aws\Result describeSharedVpcConfiguration(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeSharedVpcConfigurationAsync(array $args = [])
 * @method \Aws\Result describeSnapshots(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeSnapshotsAsync(array $args = [])
 * @method \Aws\Result describeStorageVirtualMachines(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeStorageVirtualMachinesAsync(array $args = [])
 * @method \Aws\Result describeVolumes(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeVolumesAsync(array $args = [])
 * @method \Aws\Result detachAndDeleteS3AccessPoint(array $args = [])
 * @method \GuzzleHttp\Promise\Promise detachAndDeleteS3AccessPointAsync(array $args = [])
 * @method \Aws\Result disassociateFileSystemAliases(array $args = [])
 * @method \GuzzleHttp\Promise\Promise disassociateFileSystemAliasesAsync(array $args = [])
 * @method \Aws\Result listTagsForResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listTagsForResourceAsync(array $args = [])
 * @method \Aws\Result releaseFileSystemNfsV3Locks(array $args = [])
 * @method \GuzzleHttp\Promise\Promise releaseFileSystemNfsV3LocksAsync(array $args = [])
 * @method \Aws\Result restoreVolumeFromSnapshot(array $args = [])
 * @method \GuzzleHttp\Promise\Promise restoreVolumeFromSnapshotAsync(array $args = [])
 * @method \Aws\Result startMisconfiguredStateRecovery(array $args = [])
 * @method \GuzzleHttp\Promise\Promise startMisconfiguredStateRecoveryAsync(array $args = [])
 * @method \Aws\Result tagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise tagResourceAsync(array $args = [])
 * @method \Aws\Result untagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise untagResourceAsync(array $args = [])
 * @method \Aws\Result updateDataRepositoryAssociation(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateDataRepositoryAssociationAsync(array $args = [])
 * @method \Aws\Result updateFileCache(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateFileCacheAsync(array $args = [])
 * @method \Aws\Result updateFileSystem(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateFileSystemAsync(array $args = [])
 * @method \Aws\Result updateSharedVpcConfiguration(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateSharedVpcConfigurationAsync(array $args = [])
 * @method \Aws\Result updateSnapshot(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateSnapshotAsync(array $args = [])
 * @method \Aws\Result updateStorageVirtualMachine(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateStorageVirtualMachineAsync(array $args = [])
 * @method \Aws\Result updateVolume(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateVolumeAsync(array $args = [])
 */
class FSxClient extends AwsClient {}
