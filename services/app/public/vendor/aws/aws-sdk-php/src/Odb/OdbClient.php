<?php
namespace Aws\Odb;

use Aws\AwsClient;

/**
 * This client is used to interact with the **odb** service.
 * @method \Aws\Result acceptMarketplaceRegistration(array $args = [])
 * @method \GuzzleHttp\Promise\Promise acceptMarketplaceRegistrationAsync(array $args = [])
 * @method \Aws\Result createCloudAutonomousVmCluster(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createCloudAutonomousVmClusterAsync(array $args = [])
 * @method \Aws\Result createCloudExadataInfrastructure(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createCloudExadataInfrastructureAsync(array $args = [])
 * @method \Aws\Result createCloudVmCluster(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createCloudVmClusterAsync(array $args = [])
 * @method \Aws\Result createOdbNetwork(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createOdbNetworkAsync(array $args = [])
 * @method \Aws\Result createOdbPeeringConnection(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createOdbPeeringConnectionAsync(array $args = [])
 * @method \Aws\Result deleteCloudAutonomousVmCluster(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteCloudAutonomousVmClusterAsync(array $args = [])
 * @method \Aws\Result deleteCloudExadataInfrastructure(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteCloudExadataInfrastructureAsync(array $args = [])
 * @method \Aws\Result deleteCloudVmCluster(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteCloudVmClusterAsync(array $args = [])
 * @method \Aws\Result deleteOdbNetwork(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteOdbNetworkAsync(array $args = [])
 * @method \Aws\Result deleteOdbPeeringConnection(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteOdbPeeringConnectionAsync(array $args = [])
 * @method \Aws\Result getCloudAutonomousVmCluster(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getCloudAutonomousVmClusterAsync(array $args = [])
 * @method \Aws\Result getCloudExadataInfrastructure(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getCloudExadataInfrastructureAsync(array $args = [])
 * @method \Aws\Result getCloudExadataInfrastructureUnallocatedResources(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getCloudExadataInfrastructureUnallocatedResourcesAsync(array $args = [])
 * @method \Aws\Result getCloudVmCluster(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getCloudVmClusterAsync(array $args = [])
 * @method \Aws\Result getDbNode(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getDbNodeAsync(array $args = [])
 * @method \Aws\Result getDbServer(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getDbServerAsync(array $args = [])
 * @method \Aws\Result getOciOnboardingStatus(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getOciOnboardingStatusAsync(array $args = [])
 * @method \Aws\Result getOdbNetwork(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getOdbNetworkAsync(array $args = [])
 * @method \Aws\Result getOdbPeeringConnection(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getOdbPeeringConnectionAsync(array $args = [])
 * @method \Aws\Result initializeService(array $args = [])
 * @method \GuzzleHttp\Promise\Promise initializeServiceAsync(array $args = [])
 * @method \Aws\Result listAutonomousVirtualMachines(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listAutonomousVirtualMachinesAsync(array $args = [])
 * @method \Aws\Result listCloudAutonomousVmClusters(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listCloudAutonomousVmClustersAsync(array $args = [])
 * @method \Aws\Result listCloudExadataInfrastructures(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listCloudExadataInfrastructuresAsync(array $args = [])
 * @method \Aws\Result listCloudVmClusters(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listCloudVmClustersAsync(array $args = [])
 * @method \Aws\Result listDbNodes(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listDbNodesAsync(array $args = [])
 * @method \Aws\Result listDbServers(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listDbServersAsync(array $args = [])
 * @method \Aws\Result listDbSystemShapes(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listDbSystemShapesAsync(array $args = [])
 * @method \Aws\Result listGiVersions(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listGiVersionsAsync(array $args = [])
 * @method \Aws\Result listOdbNetworks(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listOdbNetworksAsync(array $args = [])
 * @method \Aws\Result listOdbPeeringConnections(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listOdbPeeringConnectionsAsync(array $args = [])
 * @method \Aws\Result listSystemVersions(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listSystemVersionsAsync(array $args = [])
 * @method \Aws\Result listTagsForResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listTagsForResourceAsync(array $args = [])
 * @method \Aws\Result rebootDbNode(array $args = [])
 * @method \GuzzleHttp\Promise\Promise rebootDbNodeAsync(array $args = [])
 * @method \Aws\Result startDbNode(array $args = [])
 * @method \GuzzleHttp\Promise\Promise startDbNodeAsync(array $args = [])
 * @method \Aws\Result stopDbNode(array $args = [])
 * @method \GuzzleHttp\Promise\Promise stopDbNodeAsync(array $args = [])
 * @method \Aws\Result tagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise tagResourceAsync(array $args = [])
 * @method \Aws\Result untagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise untagResourceAsync(array $args = [])
 * @method \Aws\Result updateCloudExadataInfrastructure(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateCloudExadataInfrastructureAsync(array $args = [])
 * @method \Aws\Result updateOdbNetwork(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateOdbNetworkAsync(array $args = [])
 */
class OdbClient extends AwsClient {}
