<?php
namespace Aws\ConnectCampaignService;

use Aws\AwsClient;

/**
 * This client is used to interact with the **AmazonConnectCampaignService** service.
 * @method \Aws\Result createCampaign(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createCampaignAsync(array $args = [])
 * @method \Aws\Result deleteCampaign(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteCampaignAsync(array $args = [])
 * @method \Aws\Result deleteConnectInstanceConfig(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteConnectInstanceConfigAsync(array $args = [])
 * @method \Aws\Result deleteInstanceOnboardingJob(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteInstanceOnboardingJobAsync(array $args = [])
 * @method \Aws\Result describeCampaign(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeCampaignAsync(array $args = [])
 * @method \Aws\Result getCampaignState(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getCampaignStateAsync(array $args = [])
 * @method \Aws\Result getCampaignStateBatch(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getCampaignStateBatchAsync(array $args = [])
 * @method \Aws\Result getConnectInstanceConfig(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getConnectInstanceConfigAsync(array $args = [])
 * @method \Aws\Result getInstanceOnboardingJobStatus(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getInstanceOnboardingJobStatusAsync(array $args = [])
 * @method \Aws\Result listCampaigns(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listCampaignsAsync(array $args = [])
 * @method \Aws\Result listTagsForResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listTagsForResourceAsync(array $args = [])
 * @method \Aws\Result pauseCampaign(array $args = [])
 * @method \GuzzleHttp\Promise\Promise pauseCampaignAsync(array $args = [])
 * @method \Aws\Result putDialRequestBatch(array $args = [])
 * @method \GuzzleHttp\Promise\Promise putDialRequestBatchAsync(array $args = [])
 * @method \Aws\Result resumeCampaign(array $args = [])
 * @method \GuzzleHttp\Promise\Promise resumeCampaignAsync(array $args = [])
 * @method \Aws\Result startCampaign(array $args = [])
 * @method \GuzzleHttp\Promise\Promise startCampaignAsync(array $args = [])
 * @method \Aws\Result startInstanceOnboardingJob(array $args = [])
 * @method \GuzzleHttp\Promise\Promise startInstanceOnboardingJobAsync(array $args = [])
 * @method \Aws\Result stopCampaign(array $args = [])
 * @method \GuzzleHttp\Promise\Promise stopCampaignAsync(array $args = [])
 * @method \Aws\Result tagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise tagResourceAsync(array $args = [])
 * @method \Aws\Result untagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise untagResourceAsync(array $args = [])
 * @method \Aws\Result updateCampaignDialerConfig(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateCampaignDialerConfigAsync(array $args = [])
 * @method \Aws\Result updateCampaignName(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateCampaignNameAsync(array $args = [])
 * @method \Aws\Result updateCampaignOutboundCallConfig(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateCampaignOutboundCallConfigAsync(array $args = [])
 */
class ConnectCampaignServiceClient extends AwsClient {}
