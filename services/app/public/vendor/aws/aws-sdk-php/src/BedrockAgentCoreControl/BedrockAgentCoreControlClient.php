<?php
namespace Aws\BedrockAgentCoreControl;

use Aws\AwsClient;

/**
 * This client is used to interact with the **Amazon Bedrock Agent Core Control Plane Fronting Layer** service.
 * @method \Aws\Result createAgentRuntime(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createAgentRuntimeAsync(array $args = [])
 * @method \Aws\Result createAgentRuntimeEndpoint(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createAgentRuntimeEndpointAsync(array $args = [])
 * @method \Aws\Result createApiKeyCredentialProvider(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createApiKeyCredentialProviderAsync(array $args = [])
 * @method \Aws\Result createBrowser(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createBrowserAsync(array $args = [])
 * @method \Aws\Result createCodeInterpreter(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createCodeInterpreterAsync(array $args = [])
 * @method \Aws\Result createGateway(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createGatewayAsync(array $args = [])
 * @method \Aws\Result createGatewayTarget(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createGatewayTargetAsync(array $args = [])
 * @method \Aws\Result createMemory(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createMemoryAsync(array $args = [])
 * @method \Aws\Result createOauth2CredentialProvider(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createOauth2CredentialProviderAsync(array $args = [])
 * @method \Aws\Result createWorkloadIdentity(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createWorkloadIdentityAsync(array $args = [])
 * @method \Aws\Result deleteAgentRuntime(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteAgentRuntimeAsync(array $args = [])
 * @method \Aws\Result deleteAgentRuntimeEndpoint(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteAgentRuntimeEndpointAsync(array $args = [])
 * @method \Aws\Result deleteApiKeyCredentialProvider(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteApiKeyCredentialProviderAsync(array $args = [])
 * @method \Aws\Result deleteBrowser(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteBrowserAsync(array $args = [])
 * @method \Aws\Result deleteCodeInterpreter(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteCodeInterpreterAsync(array $args = [])
 * @method \Aws\Result deleteGateway(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteGatewayAsync(array $args = [])
 * @method \Aws\Result deleteGatewayTarget(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteGatewayTargetAsync(array $args = [])
 * @method \Aws\Result deleteMemory(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteMemoryAsync(array $args = [])
 * @method \Aws\Result deleteOauth2CredentialProvider(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteOauth2CredentialProviderAsync(array $args = [])
 * @method \Aws\Result deleteWorkloadIdentity(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteWorkloadIdentityAsync(array $args = [])
 * @method \Aws\Result getAgentRuntime(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getAgentRuntimeAsync(array $args = [])
 * @method \Aws\Result getAgentRuntimeEndpoint(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getAgentRuntimeEndpointAsync(array $args = [])
 * @method \Aws\Result getApiKeyCredentialProvider(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getApiKeyCredentialProviderAsync(array $args = [])
 * @method \Aws\Result getBrowser(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getBrowserAsync(array $args = [])
 * @method \Aws\Result getCodeInterpreter(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getCodeInterpreterAsync(array $args = [])
 * @method \Aws\Result getGateway(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getGatewayAsync(array $args = [])
 * @method \Aws\Result getGatewayTarget(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getGatewayTargetAsync(array $args = [])
 * @method \Aws\Result getMemory(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getMemoryAsync(array $args = [])
 * @method \Aws\Result getOauth2CredentialProvider(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getOauth2CredentialProviderAsync(array $args = [])
 * @method \Aws\Result getTokenVault(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getTokenVaultAsync(array $args = [])
 * @method \Aws\Result getWorkloadIdentity(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getWorkloadIdentityAsync(array $args = [])
 * @method \Aws\Result listAgentRuntimeEndpoints(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listAgentRuntimeEndpointsAsync(array $args = [])
 * @method \Aws\Result listAgentRuntimeVersions(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listAgentRuntimeVersionsAsync(array $args = [])
 * @method \Aws\Result listAgentRuntimes(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listAgentRuntimesAsync(array $args = [])
 * @method \Aws\Result listApiKeyCredentialProviders(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listApiKeyCredentialProvidersAsync(array $args = [])
 * @method \Aws\Result listBrowsers(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listBrowsersAsync(array $args = [])
 * @method \Aws\Result listCodeInterpreters(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listCodeInterpretersAsync(array $args = [])
 * @method \Aws\Result listGatewayTargets(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listGatewayTargetsAsync(array $args = [])
 * @method \Aws\Result listGateways(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listGatewaysAsync(array $args = [])
 * @method \Aws\Result listMemories(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listMemoriesAsync(array $args = [])
 * @method \Aws\Result listOauth2CredentialProviders(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listOauth2CredentialProvidersAsync(array $args = [])
 * @method \Aws\Result listWorkloadIdentities(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listWorkloadIdentitiesAsync(array $args = [])
 * @method \Aws\Result setTokenVaultCMK(array $args = [])
 * @method \GuzzleHttp\Promise\Promise setTokenVaultCMKAsync(array $args = [])
 * @method \Aws\Result updateAgentRuntime(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateAgentRuntimeAsync(array $args = [])
 * @method \Aws\Result updateAgentRuntimeEndpoint(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateAgentRuntimeEndpointAsync(array $args = [])
 * @method \Aws\Result updateApiKeyCredentialProvider(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateApiKeyCredentialProviderAsync(array $args = [])
 * @method \Aws\Result updateGateway(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateGatewayAsync(array $args = [])
 * @method \Aws\Result updateGatewayTarget(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateGatewayTargetAsync(array $args = [])
 * @method \Aws\Result updateMemory(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateMemoryAsync(array $args = [])
 * @method \Aws\Result updateOauth2CredentialProvider(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateOauth2CredentialProviderAsync(array $args = [])
 * @method \Aws\Result updateWorkloadIdentity(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateWorkloadIdentityAsync(array $args = [])
 */
class BedrockAgentCoreControlClient extends AwsClient {}
