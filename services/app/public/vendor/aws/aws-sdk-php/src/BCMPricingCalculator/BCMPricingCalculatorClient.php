<?php
namespace Aws\BCMPricingCalculator;

use Aws\AwsClient;

/**
 * This client is used to interact with the **AWS Billing and Cost Management Pricing Calculator** service.
 * @method \Aws\Result batchCreateBillScenarioCommitmentModification(array $args = [])
 * @method \GuzzleHttp\Promise\Promise batchCreateBillScenarioCommitmentModificationAsync(array $args = [])
 * @method \Aws\Result batchCreateBillScenarioUsageModification(array $args = [])
 * @method \GuzzleHttp\Promise\Promise batchCreateBillScenarioUsageModificationAsync(array $args = [])
 * @method \Aws\Result batchCreateWorkloadEstimateUsage(array $args = [])
 * @method \GuzzleHttp\Promise\Promise batchCreateWorkloadEstimateUsageAsync(array $args = [])
 * @method \Aws\Result batchDeleteBillScenarioCommitmentModification(array $args = [])
 * @method \GuzzleHttp\Promise\Promise batchDeleteBillScenarioCommitmentModificationAsync(array $args = [])
 * @method \Aws\Result batchDeleteBillScenarioUsageModification(array $args = [])
 * @method \GuzzleHttp\Promise\Promise batchDeleteBillScenarioUsageModificationAsync(array $args = [])
 * @method \Aws\Result batchDeleteWorkloadEstimateUsage(array $args = [])
 * @method \GuzzleHttp\Promise\Promise batchDeleteWorkloadEstimateUsageAsync(array $args = [])
 * @method \Aws\Result batchUpdateBillScenarioCommitmentModification(array $args = [])
 * @method \GuzzleHttp\Promise\Promise batchUpdateBillScenarioCommitmentModificationAsync(array $args = [])
 * @method \Aws\Result batchUpdateBillScenarioUsageModification(array $args = [])
 * @method \GuzzleHttp\Promise\Promise batchUpdateBillScenarioUsageModificationAsync(array $args = [])
 * @method \Aws\Result batchUpdateWorkloadEstimateUsage(array $args = [])
 * @method \GuzzleHttp\Promise\Promise batchUpdateWorkloadEstimateUsageAsync(array $args = [])
 * @method \Aws\Result createBillEstimate(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createBillEstimateAsync(array $args = [])
 * @method \Aws\Result createBillScenario(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createBillScenarioAsync(array $args = [])
 * @method \Aws\Result createWorkloadEstimate(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createWorkloadEstimateAsync(array $args = [])
 * @method \Aws\Result deleteBillEstimate(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteBillEstimateAsync(array $args = [])
 * @method \Aws\Result deleteBillScenario(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteBillScenarioAsync(array $args = [])
 * @method \Aws\Result deleteWorkloadEstimate(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteWorkloadEstimateAsync(array $args = [])
 * @method \Aws\Result getBillEstimate(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getBillEstimateAsync(array $args = [])
 * @method \Aws\Result getBillScenario(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getBillScenarioAsync(array $args = [])
 * @method \Aws\Result getPreferences(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getPreferencesAsync(array $args = [])
 * @method \Aws\Result getWorkloadEstimate(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getWorkloadEstimateAsync(array $args = [])
 * @method \Aws\Result listBillEstimateCommitments(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listBillEstimateCommitmentsAsync(array $args = [])
 * @method \Aws\Result listBillEstimateInputCommitmentModifications(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listBillEstimateInputCommitmentModificationsAsync(array $args = [])
 * @method \Aws\Result listBillEstimateInputUsageModifications(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listBillEstimateInputUsageModificationsAsync(array $args = [])
 * @method \Aws\Result listBillEstimateLineItems(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listBillEstimateLineItemsAsync(array $args = [])
 * @method \Aws\Result listBillEstimates(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listBillEstimatesAsync(array $args = [])
 * @method \Aws\Result listBillScenarioCommitmentModifications(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listBillScenarioCommitmentModificationsAsync(array $args = [])
 * @method \Aws\Result listBillScenarioUsageModifications(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listBillScenarioUsageModificationsAsync(array $args = [])
 * @method \Aws\Result listBillScenarios(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listBillScenariosAsync(array $args = [])
 * @method \Aws\Result listTagsForResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listTagsForResourceAsync(array $args = [])
 * @method \Aws\Result listWorkloadEstimateUsage(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listWorkloadEstimateUsageAsync(array $args = [])
 * @method \Aws\Result listWorkloadEstimates(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listWorkloadEstimatesAsync(array $args = [])
 * @method \Aws\Result tagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise tagResourceAsync(array $args = [])
 * @method \Aws\Result untagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise untagResourceAsync(array $args = [])
 * @method \Aws\Result updateBillEstimate(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateBillEstimateAsync(array $args = [])
 * @method \Aws\Result updateBillScenario(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateBillScenarioAsync(array $args = [])
 * @method \Aws\Result updatePreferences(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updatePreferencesAsync(array $args = [])
 * @method \Aws\Result updateWorkloadEstimate(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateWorkloadEstimateAsync(array $args = [])
 */
class BCMPricingCalculatorClient extends AwsClient {}
