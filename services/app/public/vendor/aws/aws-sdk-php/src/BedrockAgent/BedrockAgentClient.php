<?php
namespace Aws\BedrockAgent;

use Aws\AwsClient;

/**
 * This client is used to interact with the **Agents for Amazon Bedrock** service.
 * @method \Aws\Result associateAgentCollaborator(array $args = [])
 * @method \GuzzleHttp\Promise\Promise associateAgentCollaboratorAsync(array $args = [])
 * @method \Aws\Result associateAgentKnowledgeBase(array $args = [])
 * @method \GuzzleHttp\Promise\Promise associateAgentKnowledgeBaseAsync(array $args = [])
 * @method \Aws\Result createAgent(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createAgentAsync(array $args = [])
 * @method \Aws\Result createAgentActionGroup(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createAgentActionGroupAsync(array $args = [])
 * @method \Aws\Result createAgentAlias(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createAgentAliasAsync(array $args = [])
 * @method \Aws\Result createDataSource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createDataSourceAsync(array $args = [])
 * @method \Aws\Result createFlow(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createFlowAsync(array $args = [])
 * @method \Aws\Result createFlowAlias(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createFlowAliasAsync(array $args = [])
 * @method \Aws\Result createFlowVersion(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createFlowVersionAsync(array $args = [])
 * @method \Aws\Result createKnowledgeBase(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createKnowledgeBaseAsync(array $args = [])
 * @method \Aws\Result createPrompt(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createPromptAsync(array $args = [])
 * @method \Aws\Result createPromptVersion(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createPromptVersionAsync(array $args = [])
 * @method \Aws\Result deleteAgent(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteAgentAsync(array $args = [])
 * @method \Aws\Result deleteAgentActionGroup(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteAgentActionGroupAsync(array $args = [])
 * @method \Aws\Result deleteAgentAlias(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteAgentAliasAsync(array $args = [])
 * @method \Aws\Result deleteAgentVersion(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteAgentVersionAsync(array $args = [])
 * @method \Aws\Result deleteDataSource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteDataSourceAsync(array $args = [])
 * @method \Aws\Result deleteFlow(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteFlowAsync(array $args = [])
 * @method \Aws\Result deleteFlowAlias(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteFlowAliasAsync(array $args = [])
 * @method \Aws\Result deleteFlowVersion(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteFlowVersionAsync(array $args = [])
 * @method \Aws\Result deleteKnowledgeBase(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteKnowledgeBaseAsync(array $args = [])
 * @method \Aws\Result deleteKnowledgeBaseDocuments(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteKnowledgeBaseDocumentsAsync(array $args = [])
 * @method \Aws\Result deletePrompt(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deletePromptAsync(array $args = [])
 * @method \Aws\Result disassociateAgentCollaborator(array $args = [])
 * @method \GuzzleHttp\Promise\Promise disassociateAgentCollaboratorAsync(array $args = [])
 * @method \Aws\Result disassociateAgentKnowledgeBase(array $args = [])
 * @method \GuzzleHttp\Promise\Promise disassociateAgentKnowledgeBaseAsync(array $args = [])
 * @method \Aws\Result getAgent(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getAgentAsync(array $args = [])
 * @method \Aws\Result getAgentActionGroup(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getAgentActionGroupAsync(array $args = [])
 * @method \Aws\Result getAgentAlias(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getAgentAliasAsync(array $args = [])
 * @method \Aws\Result getAgentCollaborator(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getAgentCollaboratorAsync(array $args = [])
 * @method \Aws\Result getAgentKnowledgeBase(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getAgentKnowledgeBaseAsync(array $args = [])
 * @method \Aws\Result getAgentVersion(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getAgentVersionAsync(array $args = [])
 * @method \Aws\Result getDataSource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getDataSourceAsync(array $args = [])
 * @method \Aws\Result getFlow(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getFlowAsync(array $args = [])
 * @method \Aws\Result getFlowAlias(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getFlowAliasAsync(array $args = [])
 * @method \Aws\Result getFlowVersion(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getFlowVersionAsync(array $args = [])
 * @method \Aws\Result getIngestionJob(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getIngestionJobAsync(array $args = [])
 * @method \Aws\Result getKnowledgeBase(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getKnowledgeBaseAsync(array $args = [])
 * @method \Aws\Result getKnowledgeBaseDocuments(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getKnowledgeBaseDocumentsAsync(array $args = [])
 * @method \Aws\Result getPrompt(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getPromptAsync(array $args = [])
 * @method \Aws\Result ingestKnowledgeBaseDocuments(array $args = [])
 * @method \GuzzleHttp\Promise\Promise ingestKnowledgeBaseDocumentsAsync(array $args = [])
 * @method \Aws\Result listAgentActionGroups(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listAgentActionGroupsAsync(array $args = [])
 * @method \Aws\Result listAgentAliases(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listAgentAliasesAsync(array $args = [])
 * @method \Aws\Result listAgentCollaborators(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listAgentCollaboratorsAsync(array $args = [])
 * @method \Aws\Result listAgentKnowledgeBases(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listAgentKnowledgeBasesAsync(array $args = [])
 * @method \Aws\Result listAgentVersions(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listAgentVersionsAsync(array $args = [])
 * @method \Aws\Result listAgents(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listAgentsAsync(array $args = [])
 * @method \Aws\Result listDataSources(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listDataSourcesAsync(array $args = [])
 * @method \Aws\Result listFlowAliases(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listFlowAliasesAsync(array $args = [])
 * @method \Aws\Result listFlowVersions(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listFlowVersionsAsync(array $args = [])
 * @method \Aws\Result listFlows(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listFlowsAsync(array $args = [])
 * @method \Aws\Result listIngestionJobs(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listIngestionJobsAsync(array $args = [])
 * @method \Aws\Result listKnowledgeBaseDocuments(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listKnowledgeBaseDocumentsAsync(array $args = [])
 * @method \Aws\Result listKnowledgeBases(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listKnowledgeBasesAsync(array $args = [])
 * @method \Aws\Result listPrompts(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listPromptsAsync(array $args = [])
 * @method \Aws\Result listTagsForResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listTagsForResourceAsync(array $args = [])
 * @method \Aws\Result prepareAgent(array $args = [])
 * @method \GuzzleHttp\Promise\Promise prepareAgentAsync(array $args = [])
 * @method \Aws\Result prepareFlow(array $args = [])
 * @method \GuzzleHttp\Promise\Promise prepareFlowAsync(array $args = [])
 * @method \Aws\Result startIngestionJob(array $args = [])
 * @method \GuzzleHttp\Promise\Promise startIngestionJobAsync(array $args = [])
 * @method \Aws\Result stopIngestionJob(array $args = [])
 * @method \GuzzleHttp\Promise\Promise stopIngestionJobAsync(array $args = [])
 * @method \Aws\Result tagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise tagResourceAsync(array $args = [])
 * @method \Aws\Result untagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise untagResourceAsync(array $args = [])
 * @method \Aws\Result updateAgent(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateAgentAsync(array $args = [])
 * @method \Aws\Result updateAgentActionGroup(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateAgentActionGroupAsync(array $args = [])
 * @method \Aws\Result updateAgentAlias(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateAgentAliasAsync(array $args = [])
 * @method \Aws\Result updateAgentCollaborator(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateAgentCollaboratorAsync(array $args = [])
 * @method \Aws\Result updateAgentKnowledgeBase(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateAgentKnowledgeBaseAsync(array $args = [])
 * @method \Aws\Result updateDataSource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateDataSourceAsync(array $args = [])
 * @method \Aws\Result updateFlow(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateFlowAsync(array $args = [])
 * @method \Aws\Result updateFlowAlias(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateFlowAliasAsync(array $args = [])
 * @method \Aws\Result updateKnowledgeBase(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateKnowledgeBaseAsync(array $args = [])
 * @method \Aws\Result updatePrompt(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updatePromptAsync(array $args = [])
 * @method \Aws\Result validateFlowDefinition(array $args = [])
 * @method \GuzzleHttp\Promise\Promise validateFlowDefinitionAsync(array $args = [])
 */
class BedrockAgentClient extends AwsClient {}
