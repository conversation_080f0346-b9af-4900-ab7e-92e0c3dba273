<?php
namespace Aws\ForecastService;

use Aws\AwsClient;

/**
 * This client is used to interact with the **Amazon Forecast Service** service.
 * @method \Aws\Result createAutoPredictor(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createAutoPredictorAsync(array $args = [])
 * @method \Aws\Result createDataset(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createDatasetAsync(array $args = [])
 * @method \Aws\Result createDatasetGroup(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createDatasetGroupAsync(array $args = [])
 * @method \Aws\Result createDatasetImportJob(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createDatasetImportJobAsync(array $args = [])
 * @method \Aws\Result createExplainability(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createExplainabilityAsync(array $args = [])
 * @method \Aws\Result createExplainabilityExport(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createExplainabilityExportAsync(array $args = [])
 * @method \Aws\Result createForecast(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createForecastAsync(array $args = [])
 * @method \Aws\Result createForecastExportJob(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createForecastExportJobAsync(array $args = [])
 * @method \Aws\Result createMonitor(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createMonitorAsync(array $args = [])
 * @method \Aws\Result createPredictor(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createPredictorAsync(array $args = [])
 * @method \Aws\Result createPredictorBacktestExportJob(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createPredictorBacktestExportJobAsync(array $args = [])
 * @method \Aws\Result createWhatIfAnalysis(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createWhatIfAnalysisAsync(array $args = [])
 * @method \Aws\Result createWhatIfForecast(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createWhatIfForecastAsync(array $args = [])
 * @method \Aws\Result createWhatIfForecastExport(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createWhatIfForecastExportAsync(array $args = [])
 * @method \Aws\Result deleteDataset(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteDatasetAsync(array $args = [])
 * @method \Aws\Result deleteDatasetGroup(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteDatasetGroupAsync(array $args = [])
 * @method \Aws\Result deleteDatasetImportJob(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteDatasetImportJobAsync(array $args = [])
 * @method \Aws\Result deleteExplainability(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteExplainabilityAsync(array $args = [])
 * @method \Aws\Result deleteExplainabilityExport(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteExplainabilityExportAsync(array $args = [])
 * @method \Aws\Result deleteForecast(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteForecastAsync(array $args = [])
 * @method \Aws\Result deleteForecastExportJob(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteForecastExportJobAsync(array $args = [])
 * @method \Aws\Result deleteMonitor(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteMonitorAsync(array $args = [])
 * @method \Aws\Result deletePredictor(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deletePredictorAsync(array $args = [])
 * @method \Aws\Result deletePredictorBacktestExportJob(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deletePredictorBacktestExportJobAsync(array $args = [])
 * @method \Aws\Result deleteResourceTree(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteResourceTreeAsync(array $args = [])
 * @method \Aws\Result deleteWhatIfAnalysis(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteWhatIfAnalysisAsync(array $args = [])
 * @method \Aws\Result deleteWhatIfForecast(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteWhatIfForecastAsync(array $args = [])
 * @method \Aws\Result deleteWhatIfForecastExport(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteWhatIfForecastExportAsync(array $args = [])
 * @method \Aws\Result describeAutoPredictor(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeAutoPredictorAsync(array $args = [])
 * @method \Aws\Result describeDataset(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeDatasetAsync(array $args = [])
 * @method \Aws\Result describeDatasetGroup(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeDatasetGroupAsync(array $args = [])
 * @method \Aws\Result describeDatasetImportJob(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeDatasetImportJobAsync(array $args = [])
 * @method \Aws\Result describeExplainability(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeExplainabilityAsync(array $args = [])
 * @method \Aws\Result describeExplainabilityExport(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeExplainabilityExportAsync(array $args = [])
 * @method \Aws\Result describeForecast(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeForecastAsync(array $args = [])
 * @method \Aws\Result describeForecastExportJob(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeForecastExportJobAsync(array $args = [])
 * @method \Aws\Result describeMonitor(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeMonitorAsync(array $args = [])
 * @method \Aws\Result describePredictor(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describePredictorAsync(array $args = [])
 * @method \Aws\Result describePredictorBacktestExportJob(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describePredictorBacktestExportJobAsync(array $args = [])
 * @method \Aws\Result describeWhatIfAnalysis(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeWhatIfAnalysisAsync(array $args = [])
 * @method \Aws\Result describeWhatIfForecast(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeWhatIfForecastAsync(array $args = [])
 * @method \Aws\Result describeWhatIfForecastExport(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeWhatIfForecastExportAsync(array $args = [])
 * @method \Aws\Result getAccuracyMetrics(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getAccuracyMetricsAsync(array $args = [])
 * @method \Aws\Result listDatasetGroups(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listDatasetGroupsAsync(array $args = [])
 * @method \Aws\Result listDatasetImportJobs(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listDatasetImportJobsAsync(array $args = [])
 * @method \Aws\Result listDatasets(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listDatasetsAsync(array $args = [])
 * @method \Aws\Result listExplainabilities(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listExplainabilitiesAsync(array $args = [])
 * @method \Aws\Result listExplainabilityExports(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listExplainabilityExportsAsync(array $args = [])
 * @method \Aws\Result listForecastExportJobs(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listForecastExportJobsAsync(array $args = [])
 * @method \Aws\Result listForecasts(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listForecastsAsync(array $args = [])
 * @method \Aws\Result listMonitorEvaluations(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listMonitorEvaluationsAsync(array $args = [])
 * @method \Aws\Result listMonitors(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listMonitorsAsync(array $args = [])
 * @method \Aws\Result listPredictorBacktestExportJobs(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listPredictorBacktestExportJobsAsync(array $args = [])
 * @method \Aws\Result listPredictors(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listPredictorsAsync(array $args = [])
 * @method \Aws\Result listTagsForResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listTagsForResourceAsync(array $args = [])
 * @method \Aws\Result listWhatIfAnalyses(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listWhatIfAnalysesAsync(array $args = [])
 * @method \Aws\Result listWhatIfForecastExports(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listWhatIfForecastExportsAsync(array $args = [])
 * @method \Aws\Result listWhatIfForecasts(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listWhatIfForecastsAsync(array $args = [])
 * @method \Aws\Result resumeResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise resumeResourceAsync(array $args = [])
 * @method \Aws\Result stopResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise stopResourceAsync(array $args = [])
 * @method \Aws\Result tagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise tagResourceAsync(array $args = [])
 * @method \Aws\Result untagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise untagResourceAsync(array $args = [])
 * @method \Aws\Result updateDatasetGroup(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateDatasetGroupAsync(array $args = [])
 */
class ForecastServiceClient extends AwsClient {}
