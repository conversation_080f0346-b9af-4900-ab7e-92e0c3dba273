<?php
namespace Aws\ChimeSDKMediaPipelines;

use Aws\AwsClient;

/**
 * This client is used to interact with the **Amazon Chime SDK Media Pipelines** service.
 * @method \Aws\Result createMediaCapturePipeline(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createMediaCapturePipelineAsync(array $args = [])
 * @method \Aws\Result createMediaConcatenationPipeline(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createMediaConcatenationPipelineAsync(array $args = [])
 * @method \Aws\Result createMediaInsightsPipeline(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createMediaInsightsPipelineAsync(array $args = [])
 * @method \Aws\Result createMediaInsightsPipelineConfiguration(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createMediaInsightsPipelineConfigurationAsync(array $args = [])
 * @method \Aws\Result createMediaLiveConnectorPipeline(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createMediaLiveConnectorPipelineAsync(array $args = [])
 * @method \Aws\Result createMediaPipelineKinesisVideoStreamPool(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createMediaPipelineKinesisVideoStreamPoolAsync(array $args = [])
 * @method \Aws\Result createMediaStreamPipeline(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createMediaStreamPipelineAsync(array $args = [])
 * @method \Aws\Result deleteMediaCapturePipeline(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteMediaCapturePipelineAsync(array $args = [])
 * @method \Aws\Result deleteMediaInsightsPipelineConfiguration(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteMediaInsightsPipelineConfigurationAsync(array $args = [])
 * @method \Aws\Result deleteMediaPipeline(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteMediaPipelineAsync(array $args = [])
 * @method \Aws\Result deleteMediaPipelineKinesisVideoStreamPool(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteMediaPipelineKinesisVideoStreamPoolAsync(array $args = [])
 * @method \Aws\Result getMediaCapturePipeline(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getMediaCapturePipelineAsync(array $args = [])
 * @method \Aws\Result getMediaInsightsPipelineConfiguration(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getMediaInsightsPipelineConfigurationAsync(array $args = [])
 * @method \Aws\Result getMediaPipeline(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getMediaPipelineAsync(array $args = [])
 * @method \Aws\Result getMediaPipelineKinesisVideoStreamPool(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getMediaPipelineKinesisVideoStreamPoolAsync(array $args = [])
 * @method \Aws\Result getSpeakerSearchTask(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getSpeakerSearchTaskAsync(array $args = [])
 * @method \Aws\Result getVoiceToneAnalysisTask(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getVoiceToneAnalysisTaskAsync(array $args = [])
 * @method \Aws\Result listMediaCapturePipelines(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listMediaCapturePipelinesAsync(array $args = [])
 * @method \Aws\Result listMediaInsightsPipelineConfigurations(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listMediaInsightsPipelineConfigurationsAsync(array $args = [])
 * @method \Aws\Result listMediaPipelineKinesisVideoStreamPools(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listMediaPipelineKinesisVideoStreamPoolsAsync(array $args = [])
 * @method \Aws\Result listMediaPipelines(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listMediaPipelinesAsync(array $args = [])
 * @method \Aws\Result listTagsForResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listTagsForResourceAsync(array $args = [])
 * @method \Aws\Result startSpeakerSearchTask(array $args = [])
 * @method \GuzzleHttp\Promise\Promise startSpeakerSearchTaskAsync(array $args = [])
 * @method \Aws\Result startVoiceToneAnalysisTask(array $args = [])
 * @method \GuzzleHttp\Promise\Promise startVoiceToneAnalysisTaskAsync(array $args = [])
 * @method \Aws\Result stopSpeakerSearchTask(array $args = [])
 * @method \GuzzleHttp\Promise\Promise stopSpeakerSearchTaskAsync(array $args = [])
 * @method \Aws\Result stopVoiceToneAnalysisTask(array $args = [])
 * @method \GuzzleHttp\Promise\Promise stopVoiceToneAnalysisTaskAsync(array $args = [])
 * @method \Aws\Result tagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise tagResourceAsync(array $args = [])
 * @method \Aws\Result untagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise untagResourceAsync(array $args = [])
 * @method \Aws\Result updateMediaInsightsPipelineConfiguration(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateMediaInsightsPipelineConfigurationAsync(array $args = [])
 * @method \Aws\Result updateMediaInsightsPipelineStatus(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateMediaInsightsPipelineStatusAsync(array $args = [])
 * @method \Aws\Result updateMediaPipelineKinesisVideoStreamPool(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateMediaPipelineKinesisVideoStreamPoolAsync(array $args = [])
 */
class ChimeSDKMediaPipelinesClient extends AwsClient {}
