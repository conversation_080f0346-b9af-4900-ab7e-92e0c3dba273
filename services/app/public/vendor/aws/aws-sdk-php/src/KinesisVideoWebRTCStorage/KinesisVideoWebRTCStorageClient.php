<?php
namespace Aws\KinesisVideoWebRTCStorage;

use Aws\AwsClient;

/**
 * This client is used to interact with the **Amazon Kinesis Video WebRTC Storage** service.
 * @method \Aws\Result joinStorageSession(array $args = [])
 * @method \GuzzleHttp\Promise\Promise joinStorageSessionAsync(array $args = [])
 * @method \Aws\Result joinStorageSessionAsViewer(array $args = [])
 * @method \GuzzleHttp\Promise\Promise joinStorageSessionAsViewerAsync(array $args = [])
 */
class KinesisVideoWebRTCStorageClient extends AwsClient {}
