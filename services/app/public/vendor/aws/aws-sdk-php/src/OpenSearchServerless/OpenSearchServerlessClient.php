<?php
namespace Aws\OpenSearchServerless;

use Aws\AwsClient;

/**
 * This client is used to interact with the **OpenSearch Service Serverless** service.
 * @method \Aws\Result batchGetCollection(array $args = [])
 * @method \GuzzleHttp\Promise\Promise batchGetCollectionAsync(array $args = [])
 * @method \Aws\Result batchGetEffectiveLifecyclePolicy(array $args = [])
 * @method \GuzzleHttp\Promise\Promise batchGetEffectiveLifecyclePolicyAsync(array $args = [])
 * @method \Aws\Result batchGetLifecyclePolicy(array $args = [])
 * @method \GuzzleHttp\Promise\Promise batchGetLifecyclePolicyAsync(array $args = [])
 * @method \Aws\Result batchGetVpcEndpoint(array $args = [])
 * @method \GuzzleHttp\Promise\Promise batchGetVpcEndpointAsync(array $args = [])
 * @method \Aws\Result createAccessPolicy(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createAccessPolicyAsync(array $args = [])
 * @method \Aws\Result createCollection(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createCollectionAsync(array $args = [])
 * @method \Aws\Result createLifecyclePolicy(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createLifecyclePolicyAsync(array $args = [])
 * @method \Aws\Result createSecurityConfig(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createSecurityConfigAsync(array $args = [])
 * @method \Aws\Result createSecurityPolicy(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createSecurityPolicyAsync(array $args = [])
 * @method \Aws\Result createVpcEndpoint(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createVpcEndpointAsync(array $args = [])
 * @method \Aws\Result deleteAccessPolicy(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteAccessPolicyAsync(array $args = [])
 * @method \Aws\Result deleteCollection(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteCollectionAsync(array $args = [])
 * @method \Aws\Result deleteLifecyclePolicy(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteLifecyclePolicyAsync(array $args = [])
 * @method \Aws\Result deleteSecurityConfig(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteSecurityConfigAsync(array $args = [])
 * @method \Aws\Result deleteSecurityPolicy(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteSecurityPolicyAsync(array $args = [])
 * @method \Aws\Result deleteVpcEndpoint(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteVpcEndpointAsync(array $args = [])
 * @method \Aws\Result getAccessPolicy(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getAccessPolicyAsync(array $args = [])
 * @method \Aws\Result getAccountSettings(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getAccountSettingsAsync(array $args = [])
 * @method \Aws\Result getPoliciesStats(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getPoliciesStatsAsync(array $args = [])
 * @method \Aws\Result getSecurityConfig(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getSecurityConfigAsync(array $args = [])
 * @method \Aws\Result getSecurityPolicy(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getSecurityPolicyAsync(array $args = [])
 * @method \Aws\Result listAccessPolicies(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listAccessPoliciesAsync(array $args = [])
 * @method \Aws\Result listCollections(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listCollectionsAsync(array $args = [])
 * @method \Aws\Result listLifecyclePolicies(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listLifecyclePoliciesAsync(array $args = [])
 * @method \Aws\Result listSecurityConfigs(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listSecurityConfigsAsync(array $args = [])
 * @method \Aws\Result listSecurityPolicies(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listSecurityPoliciesAsync(array $args = [])
 * @method \Aws\Result listTagsForResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listTagsForResourceAsync(array $args = [])
 * @method \Aws\Result listVpcEndpoints(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listVpcEndpointsAsync(array $args = [])
 * @method \Aws\Result tagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise tagResourceAsync(array $args = [])
 * @method \Aws\Result untagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise untagResourceAsync(array $args = [])
 * @method \Aws\Result updateAccessPolicy(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateAccessPolicyAsync(array $args = [])
 * @method \Aws\Result updateAccountSettings(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateAccountSettingsAsync(array $args = [])
 * @method \Aws\Result updateCollection(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateCollectionAsync(array $args = [])
 * @method \Aws\Result updateLifecyclePolicy(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateLifecyclePolicyAsync(array $args = [])
 * @method \Aws\Result updateSecurityConfig(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateSecurityConfigAsync(array $args = [])
 * @method \Aws\Result updateSecurityPolicy(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateSecurityPolicyAsync(array $args = [])
 * @method \Aws\Result updateVpcEndpoint(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateVpcEndpointAsync(array $args = [])
 */
class OpenSearchServerlessClient extends AwsClient {}
